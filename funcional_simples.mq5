//+------------------------------------------------------------------+
//|                               Painel de Sinais de Alta Confluência |
//|                                  Versão 3.3 - Professional Black   |
//|                               Design Premium com Tema Sofisticado   |
//+------------------------------------------------------------------+
#property copyright "Painel de Sinais de Alta Confluência v3.3 - Professional Black Edition"
#property version   "3.30"
#property description "Dashboard profissional com design premium e esquema de cores sofisticado"
#property indicator_chart_window
#property indicator_plots 0

//+------------------------------------------------------------------+
//| Parâmetros de Entrada - Design Premium                          |
//+------------------------------------------------------------------+
// --- Configurações Gerais ---
input group "⚙️ CONFIGURAÇÕES GERAIS DO PAINEL"
input int    PainelX = 20;                     // Posição X do painel
input int    PainelY = 20;                     // Posição Y do painel
input int    PainelLargura = 1880;             // Largura do painel (otimizado para FHD)
input int    PainelAltura = 900;               // Altura do painel (otimizado para FHD)
input int    UpdateSeconds = 15;               // Atualização em segundos
input int    TamanhoFonte = 10;                // Tamanho da fonte padrão

// --- Cores do Painel Principal - Esquema Premium ---
input group "🎨 CORES DO PAINEL PRINCIPAL - PREMIUM BLACK"
input color  CorFundoPainel = C'18,20,24';      // Fundo principal elegante
input color  CorFundoSecundario = C'25,28,35';  // Fundo secundário sofisticado
input color  CorBordaPrincipal = C'45,52,62';   // Borda principal refinada
input color  CorBordaDestaque = C'0,150,255';   // Borda de destaque azul vibrante
input color  CorTextoTitulo = C'255,255,255';   // Títulos em branco puro
input color  CorTextoCorpo = C'220,225,235';    // Texto corpo cinza claro
input color  CorTextoSecundario = C'160,170,185'; // Texto secundário

// --- Cores da Seção de Sinal - Premium ---
input group "⚡ CORES DA SEÇÃO DE SINAL (PREMIUM)"
input color  CorFundoSinal = C'25,28,35';       // Fundo sinal sofisticado
input color  CorTituloSinal = C'0,255,127';     // Verde premium para sinal
input color  CorTextoAcao = C'255,193,7';       // Dourado para ação
input color  CorTextoMotivo = C'200,210,220';   // Cinza claro para motivo
input color  CorGatilho = C'0,150,255';         // Azul vibrante para gatilho

// --- Cores da Matriz Estratégica - Sofisticada ---
input group "📊 CORES DA MATRIZ ESTRATÉGICA (SOFISTICADA)"
input color  CorFundoHeaderMatriz = C'35,42,52';// Header matriz elegante
input color  CorTextoHeaderMatriz = C'255,255,255';   // Header texto branco
input color  CorFundoLinhaPar = C'22,25,30';    // Linhas pares discretas
input color  CorFundoLinhaImpar = C'28,32,38';  // Linhas ímpares contrastantes
input color  CorLinhaDestaque = C'0,150,255';   // Linha de destaque azul
input color  CorForcaAlta = C'0,255,127';       // Verde sofisticado para força
input color  CorForcaBaixa = C'255,69,58';      // Vermelho elegante para fraqueza
input color  CorForcaNeutra = C'120,130,145';   // Cinza neutro refinado

// --- Cores da Grelha Tática M15 - Premium ---
input group "💰 CORES DA GRELHA TÁTICA M15 (PREMIUM)"
input color  CorFundoBloco = C'25,28,35';       // Fundo bloco sofisticado
input color  CorFundoHeaderBloco= C'35,42,52';  // Header bloco elegante
input color  CorBordaBloco = C'45,52,62';       // Borda bloco refinada
input color  CorTituloBloco = C'255,255,255';   // Título bloco branco puro
input color  CorQualidadeIdeal = C'0,255,127';  // Verde premium para IDEAL
input color  CorQualidadeBom = C'255,193,7';    // Dourado para BOM
input color  CorQualidadeTardio= C'255,99,71';  // Laranja elegante para TARDIO
input color  CorQualidadeNula = C'120,130,145'; // Cinza para nulo
input color  CorTendenciaAlta = C'0,255,127';   // Verde para alta
input color  CorTendenciaBaixa = C'255,69,58';  // Vermelho para baixa
input color  CorTendenciaLateral= C'255,193,7'; // Dourado para lateral

//+------------------------------------------------------------------+
//| Constantes e Estruturas
//+------------------------------------------------------------------+
#define MAX_PAIRS 28
#define MAX_CURRENCIES 8

// Estrutura para resultado da análise de um par
struct PairAnalysisResult
{
    int    trend;        // 1=Alta, -1=Baixa, 0=Lateral
    int    score;        // Score de continuidade
};

// Estrutura para dados consolidados de uma moeda
struct CurrencyData
{
    string name;
    int    h4_strength;
    double h4_avg_score;
    int    d1_strength;
    double d1_avg_score;
    int    w1_strength;
    double w1_avg_score;
};

// Estrutura para informação de qualidade da entrada - Premium
struct QualityInfo
{
    string text;
    color  clr;
    string emoji;
};

//+------------------------------------------------------------------+
//| Variáveis Globais
//+------------------------------------------------------------------+
string currencies[MAX_CURRENCIES] = {"USD", "EUR", "GBP", "JPY", "CHF", "CAD", "AUD", "NZD"};
string base_pairs[MAX_PAIRS] = {
    "EURUSDm", "GBPUSDm", "AUDUSDm", "NZDUSDm", "USDCADm", "USDCHFm", "USDJPYm",
    "EURGBPm", "EURAUDm", "EURNZDm", "EURCADm", "EURCHFm", "EURJPYm",
    "GBPAUDm", "GBPNZDm", "GBPCADm", "GBPCHFm", "GBPJPYm",
    "AUDNZDm", "AUDCADm", "AUDCHFm", "AUDJPYm",
    "NZDCADm", "NZDCHFm", "NZDJPYm",
    "CADCHFm", "CADJPYm",
    "CHFJPYm"
};
string normalized_pairs[MAX_PAIRS];

CurrencyData currencyData[MAX_CURRENCIES];

// Cache de análise para todos os pares e timeframes
struct AnalysisCache
{
    PairAnalysisResult h4;
    PairAnalysisResult d1;
    PairAnalysisResult w1;
    PairAnalysisResult m15;
};
AnalysisCache analysisCache[MAX_PAIRS];

datetime lastUpdate = 0;
MqlTick  last_tick;
string   obj_prefix = "pconf3_";

//+------------------------------------------------------------------+
//| Função de Inicialização do Indicador                             |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("🚀 Inicializando Painel de Sinais v3.3 Professional Black Edition...");
    Print("📊 Símbolo atual: ", _Symbol);

    Print("🔧 Etapa 1: Normalizando nomes dos pares...");
    NormalizeAllPairNames();

    Print("🔧 Etapa 2: Atualizando dados de análise...");
    UpdateAllData();

    Print("🔧 Etapa 3: Atualizando display premium...");
    UpdateDisplay();

    Print("⏰ Configurando timer para ", UpdateSeconds, " segundos");
    EventSetTimer(UpdateSeconds);

    Print("✅ Painel inicializado com sucesso!");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Função de Desinicialização                                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    EventKillTimer();
    ObjectsDeleteAll(0, obj_prefix);
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Evento do Timer (Coração do Indicador)                           |
//+------------------------------------------------------------------+
void OnTimer()
{
    // Apenas atualiza se houver um novo tick para economizar CPU
    if(SymbolInfoTick(_Symbol, last_tick) && last_tick.time > lastUpdate)
    {
        UpdateAllData();
        UpdateDisplay();
        lastUpdate = last_tick.time;
    }
}

//+------------------------------------------------------------------+
//| LÓGICA DE CÁLCULO (Engine)
//+------------------------------------------------------------------+

// Analisa um único par em um timeframe específico
void CalculatePairAnalysis(string symbol, PairAnalysisResult &result, ENUM_TIMEFRAMES timeframe)
{
    // Reset
    result.trend = 0;
    result.score = 0;

    if(symbol == "") return;

    double ema5[], ema12[];
    ArraySetAsSeries(ema5, true);
    ArraySetAsSeries(ema12, true);
    int bars_to_check = 150;

    int handle_ema5 = iMA(symbol, timeframe, 5, 0, MODE_EMA, PRICE_CLOSE);
    int handle_ema12 = iMA(symbol, timeframe, 12, 0, MODE_EMA, PRICE_CLOSE);
    
    if(handle_ema5 == INVALID_HANDLE || handle_ema12 == INVALID_HANDLE) return;

    if(CopyBuffer(handle_ema5, 0, 0, bars_to_check, ema5) < 3 ||
       CopyBuffer(handle_ema12, 0, 0, bars_to_check, ema12) < 3) return;

    double price = SymbolInfoDouble(symbol, SYMBOL_ASK);
    if(price == 0) return;
    
    // Filtro de ruído para mercado lateral
    double distance = MathAbs(ema5[0] - ema12[0]) / price * 100;
    result.trend = (distance < 0.005) ? 0 : ((ema5[0] > ema12[0]) ? 1 : -1);

    // Se houver tendência, calcula o score de continuidade
    if(result.trend != 0)
    {
        int score = 0;
        int min_size = MathMin(ArraySize(ema5), ArraySize(ema12));
        
        for(int i = 0; i < bars_to_check && i < min_size; i++)
        {
            if(((ema5[i] > ema12[i]) ? 1 : -1) == result.trend) score++;
            else break;
        }
        result.score = score;
    }
}

// Atualiza todos os dados de pares e moedas
void UpdateAllData()
{
    // 1. Analisa todos os pares em todos os timeframes e guarda no cache
    for(int i = 0; i < MAX_PAIRS; i++)
    {
        CalculatePairAnalysis(normalized_pairs[i], analysisCache[i].h4,  PERIOD_H4);
        CalculatePairAnalysis(normalized_pairs[i], analysisCache[i].d1,  PERIOD_D1);
        CalculatePairAnalysis(normalized_pairs[i], analysisCache[i].w1,  PERIOD_W1);
        CalculatePairAnalysis(normalized_pairs[i], analysisCache[i].m15, PERIOD_M15);
    }
    
    // 2. Calcula a força agregada para cada moeda
    for(int i = 0; i < MAX_CURRENCIES; i++)
    {
        string currency = currencies[i];
        currencyData[i].name = currency;
        
        // Reseta os contadores
        currencyData[i].h4_strength = 0; currencyData[i].h4_avg_score = 0;
        currencyData[i].d1_strength = 0; currencyData[i].d1_avg_score = 0;
        currencyData[i].w1_strength = 0; currencyData[i].w1_avg_score = 0;
        int h4_count = 0, d1_count = 0, w1_count = 0;

        for(int p = 0; p < MAX_PAIRS; p++)
        {
            if(normalized_pairs[p] == "") continue;
            
            string base = StringSubstr(base_pairs[p], 0, 3);
            string quote = StringSubstr(base_pairs[p], 3, 3);
            if(base != currency && quote != currency) continue;

            // Lógica de direção: se a moeda é base, a direção é direta. Se é cotação, é invertida.
            int h4_direction = (base == currency) ? analysisCache[p].h4.trend : -analysisCache[p].h4.trend;
            currencyData[i].h4_strength += h4_direction;
            if(analysisCache[p].h4.trend != 0) { currencyData[i].h4_avg_score += analysisCache[p].h4.score; h4_count++; }

            int d1_direction = (base == currency) ? analysisCache[p].d1.trend : -analysisCache[p].d1.trend;
            currencyData[i].d1_strength += d1_direction;
            if(analysisCache[p].d1.trend != 0) { currencyData[i].d1_avg_score += analysisCache[p].d1.score; d1_count++; }
            
            int w1_direction = (base == currency) ? analysisCache[p].w1.trend : -analysisCache[p].w1.trend;
            currencyData[i].w1_strength += w1_direction;
            if(analysisCache[p].w1.trend != 0) { currencyData[i].w1_avg_score += analysisCache[p].w1.score; w1_count++; }
        }
        
        // Calcula a média dos scores
        if(h4_count > 0) currencyData[i].h4_avg_score /= h4_count;
        if(d1_count > 0) currencyData[i].d1_avg_score /= d1_count;
        if(w1_count > 0) currencyData[i].w1_avg_score /= w1_count;
    }
    
    // 3. Ordena as moedas pela força D1 (a mais forte primeiro)
    SortCurrenciesByStrength();
}

// Algoritmo de ordenação para a matriz
void SortCurrenciesByStrength()
{
    for(int i = 0; i < MAX_CURRENCIES - 1; i++)
    {
        for(int j = i + 1; j < MAX_CURRENCIES; j++)
        {
            // Ordena do mais forte (positivo maior) para o mais fraco (negativo menor)
            if(currencyData[i].d1_strength < currencyData[j].d1_strength)
            {
                CurrencyData temp = currencyData[i];
                currencyData[i] = currencyData[j];
                currencyData[j] = temp;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| FUNÇÕES DE UI (CRIAÇÃO E ATUALIZAÇÃO DOS OBJETOS)
//+------------------------------------------------------------------+

// Função principal que orquestra o desenho do painel
void UpdateDisplay()
{
    // Limpa objetos antigos para redesenhar
    ObjectsDeleteAll(0, obj_prefix);

    // 1. Desenha a base do painel
    CreateRectangle(obj_prefix + "bg", PainelX, PainelY, PainelLargura, PainelAltura, CorFundo);

    // 2. Desenha as três seções principais
    DrawSignalSection();
    DrawStrategicMatrix();
    DrawTacticalGrid();
    
    ChartRedraw();
}

// SEÇÃO 1: SINAL DE ALTA CONFLUÊNCIA
void DrawSignalSection()
{
    int x = PainelX + 10;
    int y = PainelY + 10;
    
    CreateRectangle(obj_prefix + "signal_bg", x, y, PainelLargura - 20, 50, CorBorda);
    CreateLabel(obj_prefix + "signal_title", "PAINEL DE SINAIS DE ALTA CONFLUÊNCIA (V3.1)", x + 10, y + 5, clrWhite, 12, true);

    CurrencyData strongest = currencyData[0];
    CurrencyData weakest = currencyData[MAX_CURRENCIES - 1];
    
    int pair_index = FindPairIndex(strongest.name, weakest.name);
    if(pair_index == -1) return;

    string signal_pair = normalized_pairs[pair_index];
    PairAnalysisResult m15_trigger = analysisCache[pair_index].m15;

    // Ação Recomendada
    string action_text = "AÇÃO RECOMENDADA: COMPRAR " + signal_pair;
    CreateLabel(obj_prefix + "signal_action", action_text, x + 10, y + 28, clrWhite, 9, true);
    
    // Motivo
    string reason_text = "MOTIVO: " + strongest.name + " Força Máx. (" + StringFormat("%+d", strongest.d1_strength) + " D1) vs " +
                         weakest.name + " Fraqueza Máx. (" + StringFormat("%+d", weakest.d1_strength) + " D1)";
    CreateLabel(obj_prefix + "signal_reason", reason_text, x + 400, y + 28, clrWhite, 9);
    
    // Gatilho M15
    string trigger_symbol = GetTrendSymbol(m15_trigger.trend);
    QualityInfo q_info = GetQualityInfo(m15_trigger.score, m15_trigger.trend);
    string trigger_text = "GATILHO M15: " + trigger_symbol + " (Scr: " + IntegerToString(m15_trigger.score) + " - " + q_info.text + ")";
    CreateLabel(obj_prefix + "signal_trigger_text", trigger_text, x + 950, y + 28, q_info.clr, 9, true);
}

// SEÇÃO 2: MATRIZ ESTRATÉGICA
void DrawStrategicMatrix()
{
    int x = PainelX + 10;
    int y = PainelY + 75;

    CreateLabel(obj_prefix + "matrix_title", "MATRIZ ESTRATÉGICA (VISÃO MACRO)", x, y, CorTexto, 10, true);

    int start_y = y + 25;
    int col_widths[] = {60, 70, 70, 70, 70, 70, 70, 350};
    string headers[] = {"Moeda", "H4 Str", "H4 Scr", "D1 Str", "D1 Scr", "W1 Str", "W1 Scr", "Consistência (H4/D1/W1)"};
    
    int current_x = x + 10;
    int table_width = 0;
    for(int i=0; i<ArraySize(col_widths); i++) table_width += col_widths[i];

    // Desenha cabeçalho e linhas de grade
    CreateRectangle(obj_prefix + "matrix_header_bg", x, start_y, table_width + 20, 25, CorBorda);
    for(int i = 0; i < ArraySize(headers); i++)
    {
        CreateLabel(obj_prefix + "h_" + headers[i], headers[i], current_x, start_y + 5, clrWhite, TamanhoFonte, true, ANCHOR_LEFT);
        current_x += col_widths[i];
    }
    
    // Desenha linhas da tabela
    for(int i = 0; i < MAX_CURRENCIES; i++)
    {
        int row_y = start_y + 25 + (i * 25);
        current_x = x + 10;
        
        CreateRectangle(obj_prefix + "matrix_row_bg_" + IntegerToString(i), x, row_y, table_width + 20, 25, (i%2==0) ? C'25,25,35' : C'30,30,40');
        
        CurrencyData cd = currencyData[i];
        string i_str = IntegerToString(i);

        // Colunas de dados
        CreateLabel(obj_prefix+"c_name_"+i_str, cd.name, current_x, row_y + 5, CorTexto, TamanhoFonte, true); current_x += col_widths[0];
        CreateLabel(obj_prefix+"c_h4s_"+i_str, StringFormat("%+d", cd.h4_strength), current_x, row_y + 5, GetScoreColor(cd.h4_strength), TamanhoFonte, true); current_x += col_widths[1];
        CreateLabel(obj_prefix+"c_h4scr_"+i_str, DoubleToString(cd.h4_avg_score, 1), current_x, row_y + 5, CorTexto, TamanhoFonte); current_x += col_widths[2];
        CreateLabel(obj_prefix+"c_d1s_"+i_str, StringFormat("%+d", cd.d1_strength), current_x, row_y + 5, GetScoreColor(cd.d1_strength), TamanhoFonte, true); current_x += col_widths[3];
        CreateLabel(obj_prefix+"c_d1scr_"+i_str, DoubleToString(cd.d1_avg_score, 1), current_x, row_y + 5, CorTexto, TamanhoFonte); current_x += col_widths[4];
        CreateLabel(obj_prefix+"c_w1s_"+i_str, StringFormat("%+d", cd.w1_strength), current_x, row_y + 5, GetScoreColor(cd.w1_strength), TamanhoFonte, true); current_x += col_widths[5];
        CreateLabel(obj_prefix+"c_w1scr_"+i_str, DoubleToString(cd.w1_avg_score, 1), current_x, row_y + 5, CorTexto, TamanhoFonte); current_x += col_widths[6];

        // Coluna de Consistência
        string consistency_text = GetConsistencyString(cd);
        CreateLabel(obj_prefix+"c_cons_"+i_str, consistency_text, current_x, row_y + 5, CorTexto, TamanhoFonte, false);
    }
}

// SEÇÃO 3: GRELHA TÁTICA M15
void DrawTacticalGrid()
{
    int x = PainelX + 10;
    int y = PainelY + 335;

    CreateLabel(obj_prefix + "grid_title", "GRELHA TÁTICA M15 EM BLOCOS (LAYOUT PANORÂMICO)", x, y, CorTexto, 10, true);

    int block_width = 335;
    int block_height = 140;
    int x_gap = 10;
    int y_gap = 10;

    int currency_index = 0;
    for(int row = 0; row < 2; row++)
    {
        for(int col = 0; col < 4; col++)
        {
            if(currency_index >= MAX_CURRENCIES) break;

            int block_x = x + col * (block_width + x_gap);
            int block_y = y + 25 + row * (block_height + y_gap);
            
            string currency = currencies[currency_index];

            // Desenha o bloco
            CreateRectangle(obj_prefix + "block_bg_" + currency, block_x, block_y, block_width, block_height, C'25,25,35');
            CreateRectangle(obj_prefix + "block_head_" + currency, block_x, block_y, block_width, 20, CorBorda);
            CreateLabel(obj_prefix + "block_title_" + currency, currency, block_x + 10, block_y + 2, clrWhite, TamanhoFonte, true);
            
            int pair_count = 0;
            for(int p = 0; p < MAX_PAIRS; p++)
            {
                if(normalized_pairs[p] == "") continue;

                string base = StringSubstr(base_pairs[p], 0, 3);
                string quote = StringSubstr(base_pairs[p], 3, 3);

                if(base == currency || quote == currency)
                {
                    int line_y = block_y + 25 + (pair_count * 15);
                    string p_str = IntegerToString(p);

                    // Lógica de inversão: A tendência é em relação à moeda do bloco
                    int trend = analysisCache[p].m15.trend;
                    if(quote == currency) trend *= -1; // Inverte se a moeda do bloco for a de cotação

                    PairAnalysisResult m15_data = analysisCache[p].m15;
                    QualityInfo q_info = GetQualityInfo(m15_data.score, m15_data.trend);
                    
                    string line_text = StringFormat("%-7s %s  Scr: %-3d (%s)", normalized_pairs[p], GetTrendSymbol(trend), m15_data.score, q_info.text);
                    CreateLabel(obj_prefix + "pair_" + currency + p_str, line_text, block_x + 10, line_y, CorTexto, TamanhoFonte, false);

                    // Colore a parte da qualidade
                    string quality_text = "(" + q_info.text + ")";
                    CreateLabel(obj_prefix + "pair_q_" + currency + p_str, quality_text, block_x + 280, line_y, q_info.clr, TamanhoFonte, true, ANCHOR_LEFT);


                    pair_count++;
                }
            }
            currency_index++;
        }
    }
}


//+------------------------------------------------------------------+
//| FUNÇÕES UTILITÁRIAS E DE FORMATAÇÃO
//+------------------------------------------------------------------+

// Normaliza os nomes dos pares para a corretora atual
void NormalizeAllPairNames()
{
    for(int i = 0; i < MAX_PAIRS; i++)
    {
        normalized_pairs[i] = NormalizeSymbol(base_pairs[i]);
    }
}

string NormalizeSymbol(string base_symbol)
{
    if(SymbolInfoInteger(base_symbol, SYMBOL_EXIST)) { SymbolSelect(base_symbol, true); return base_symbol; }
    string lower = base_symbol; StringToLower(lower);
    if(SymbolInfoInteger(lower, SYMBOL_EXIST)) { SymbolSelect(lower, true); return lower; }
    
    int total = SymbolsTotal(false);
    for(int i = 0; i < total; i++) {
        string market_symbol = SymbolName(i, false);
        if(StringFind(StringToLower(market_symbol), lower) != -1) {
            SymbolSelect(market_symbol, true);
            return market_symbol;
        }
    }
    return "";
}

// Retorna o símbolo e cor da tendência
string GetTrendSymbol(int trend) { return (trend == 1) ? "▲" : (trend == -1) ? "▼" : "●"; }
color GetTrendColor(int trend) { return (trend == 1) ? clrForestGreen : (trend == -1) ? clrFireBrick : clrGoldenrod; }
color GetScoreColor(int score) { return (score > 0) ? clrForestGreen : (score < 0) ? clrFireBrick : clrGray; }

// Retorna o texto e cor da qualidade da entrada
QualityInfo GetQualityInfo(int score, int trend)
{
    QualityInfo info;
    if(trend == 0) {
        info.text = "---";
        info.clr = clrGray;
    } else {
        if(score <= 3) {
            info.text = "IDEAL";
            info.clr = clrSeaGreen;
        } else if(score <= 7) {
            info.text = "BOM";
            info.clr = clrGoldenrod;
        } else {
            info.text = "TARDIO";
            info.clr = clrIndianRed;
        }
    }
    return info;
}

// Cria a string de consistência para a matriz
string GetConsistencyString(const CurrencyData &cd)
{
    string h4_sym = (cd.h4_strength >= 5) ? "🟢" : (cd.h4_strength <= -5) ? "🔴" : "🟡";
    string d1_sym = (cd.d1_strength >= 5) ? "🟢" : (cd.d1_strength <= -5) ? "🔴" : "🟡";
    string w1_sym = (cd.w1_strength >= 5) ? "🟢" : (cd.w1_strength <= -5) ? "🔴" : "🟡";
    
    string result = h4_sym + d1_sym + w1_sym;
    
    if(result == "🟢🟢🟢") result += "   FORÇA TOTAL (Alinhamento Perfeito)";
    else if(result == "🔴🔴🔴") result += "   FRAQUEZA TOTAL (Alinhamento Perfeito)";
    else if(h4_sym == "🟢" && d1_sym == "🟢") result += "   FORÇA DOMINANTE";
    else if(h4_sym == "🔴" && d1_sym == "🔴") result += "   FRAQUEZA DOMINANTE";
    else result += "   SINAIS MISTOS";

    return result;
}

// Encontra o índice de um par baseado em duas moedas
int FindPairIndex(string c1, string c2)
{
    for(int i=0; i<MAX_PAIRS; i++)
    {
        if((StringFind(base_pairs[i], c1) != -1) && (StringFind(base_pairs[i], c2) != -1))
        {
            return i;
        }
    }
    return -1;
}

// Funções auxiliares para criar objetos no gráfico
void CreateLabel(string name, string text, int x, int y, color clr, int font_size, bool bold=false, ENUM_ANCHOR_POINT anchor=ANCHOR_LEFT)
{
    ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetString(0, name, OBJPROP_FONT, bold ? "Calibri Bold" : "Calibri");
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, anchor);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
}

void CreateRectangle(string name, int x, int y, int x_size, int y_size, color clr)
{
    ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, name, OBJPROP_XSIZE, x_size);
    ObjectSetInteger(0, name, OBJPROP_YSIZE, y_size);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_BACK, true);
}

// OnCalculate não é usado para a lógica principal
int OnCalculate(const int rates_total, const int prev_calculated, const datetime &time[],
                const double &open[], const double &high[], const double &low[], const double &close[],
                const long &tick_volume[], const long &volume[], const int &spread[])
{
    return(rates_total);
}
//+------------------------------------------------------------------+