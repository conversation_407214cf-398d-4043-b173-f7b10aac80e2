//+------------------------------------------------------------------+
//|                               Painel de Sinais de Alta Confluência |
//|                                  Versão 3.3 - Professional Black   |
//|                               Design Premium com Tema Sofisticado   |
//+------------------------------------------------------------------+
#property copyright "Painel de Sinais de Alta Confluência v3.3 - Professional Black Edition"
#property version   "3.30"
#property description "Dashboard profissional com design premium e esquema de cores sofisticado"
#property indicator_chart_window
#property indicator_plots 0

//+------------------------------------------------------------------+
//| Parâmetros de Entrada - Design Premium                          |
//+------------------------------------------------------------------+
// --- Configurações Gerais ---
input group "⚙️ CONFIGURAÇÕES GERAIS DO PAINEL"
input int    PainelX = 20;                     // Posição X do painel
input int    PainelY = 20;                     // Posição Y do painel
input int    PainelLargura = 1880;             // Largura do painel (otimizado para FHD)
input int    PainelAltura = 900;               // Altura do painel (otimizado para FHD)
input int    UpdateSeconds = 15;               // Atualização em segundos
input int    TamanhoFonte = 10;                // Tamanho da fonte padrão

// --- Cores do Painel Principal - Esquema Premium ---
input group "🎨 CORES DO PAINEL PRINCIPAL - PREMIUM BLACK"
input color  CorFundoPainel = C'18,20,24';      // Fundo principal elegante
input color  CorFundoSecundario = C'25,28,35';  // Fundo secundário sofisticado
input color  CorBordaPrincipal = C'45,52,62';   // Borda principal refinada
input color  CorBordaDestaque = C'0,150,255';   // Borda de destaque azul vibrante
input color  CorTextoTitulo = C'255,255,255';   // Títulos em branco puro
input color  CorTextoCorpo = C'220,225,235';    // Texto corpo cinza claro
input color  CorTextoSecundario = C'160,170,185'; // Texto secundário

// --- Cores da Seção de Sinal - Premium ---
input group "⚡ CORES DA SEÇÃO DE SINAL (PREMIUM)"
input color  CorFundoSinal = C'25,28,35';       // Fundo sinal sofisticado
input color  CorTituloSinal = C'0,255,127';     // Verde premium para sinal
input color  CorTextoAcao = C'255,193,7';       // Dourado para ação
input color  CorTextoMotivo = C'200,210,220';   // Cinza claro para motivo
input color  CorGatilho = C'0,150,255';         // Azul vibrante para gatilho

// --- Cores da Matriz Estratégica - Sofisticada ---
input group "📊 CORES DA MATRIZ ESTRATÉGICA (SOFISTICADA)"
input color  CorFundoHeaderMatriz = C'35,42,52';// Header matriz elegante
input color  CorTextoHeaderMatriz = C'255,255,255';   // Header texto branco
input color  CorFundoLinhaPar = C'22,25,30';    // Linhas pares discretas
input color  CorFundoLinhaImpar = C'28,32,38';  // Linhas ímpares contrastantes
input color  CorLinhaDestaque = C'0,150,255';   // Linha de destaque azul
input color  CorForcaAlta = C'0,255,127';       // Verde sofisticado para força
input color  CorForcaBaixa = C'255,69,58';      // Vermelho elegante para fraqueza
input color  CorForcaNeutra = C'120,130,145';   // Cinza neutro refinado

// --- Cores da Grelha Tática M15 - Premium ---
input group "💰 CORES DA GRELHA TÁTICA M15 (PREMIUM)"
input color  CorFundoBloco = C'25,28,35';       // Fundo bloco sofisticado
input color  CorFundoHeaderBloco= C'35,42,52';  // Header bloco elegante
input color  CorBordaBloco = C'45,52,62';       // Borda bloco refinada
input color  CorTituloBloco = C'255,255,255';   // Título bloco branco puro
input color  CorQualidadeIdeal = C'0,255,127';  // Verde premium para IDEAL
input color  CorQualidadeBom = C'255,193,7';    // Dourado para BOM
input color  CorQualidadeTardio= C'255,99,71';  // Laranja elegante para TARDIO
input color  CorQualidadeNula = C'120,130,145'; // Cinza para nulo
input color  CorTendenciaAlta = C'0,255,127';   // Verde para alta
input color  CorTendenciaBaixa = C'255,69,58';  // Vermelho para baixa
input color  CorTendenciaLateral= C'255,193,7'; // Dourado para lateral

//+------------------------------------------------------------------+
//| Constantes e Estruturas                                          |
//+------------------------------------------------------------------+
#define MAX_PAIRS 28
#define MAX_CURRENCIES 8

struct PairAnalysisResult {
   int trend;
   int score;
};

struct CurrencyData {
   string name;
   int h4_strength;
   double h4_avg_score;
   int d1_strength;
   double d1_avg_score;
   int w1_strength;
   double w1_avg_score;
};

struct QualityInfo {
   string text;
   color clr;
   string emoji;
};

struct AnalysisCache {
   PairAnalysisResult h4, d1, w1, m15;
};

//+------------------------------------------------------------------+
//| Variáveis Globais                                                |
//+------------------------------------------------------------------+
string currencies[MAX_CURRENCIES] = {"USD", "EUR", "GBP", "JPY", "CHF", "CAD", "AUD", "NZD"};
string base_pairs[MAX_PAIRS] = {
   "EURUSDm", "GBPUSDm", "USDJPYm", "USDCHFm", "AUDUSDm", "USDCADm", "NZDUSDm",
   "EURGBPm", "EURJPYm", "EURCHFm", "EURAUDm", "EURCADm", "EURNZDm",
   "GBPJPYm", "GBPCHFm", "GBPAUDm", "GBPCADm", "GBPNZDm",
   "CHFJPYm", "AUDJPYm", "CADJPYm", "NZDJPYm",
   "AUDCHFm", "CADCHFm", "NZDCHFm",
   "AUDCADm", "AUDNZDm", "NZDCADm"
};
string normalized_pairs[MAX_PAIRS];
CurrencyData currencyData[MAX_CURRENCIES];
AnalysisCache analysisCache[MAX_PAIRS];
datetime lastUpdate = 0;
MqlTick  last_tick;
string   obj_prefix = "pconf3_";

//+------------------------------------------------------------------+
//| Funções de Inicialização, Desinicialização e Timer              |
//+------------------------------------------------------------------+
int OnInit() {
   Print("🚀 Inicializando Painel de Sinais v3.3 Professional Black Edition...");
   NormalizeAllPairNames();
   UpdateAllData();
   UpdateDisplay();
   EventSetTimer(UpdateSeconds);
   return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason) {
   EventKillTimer();
   ObjectsDeleteAll(0, obj_prefix);
   ChartRedraw();
}

void OnTimer() {
   if(SymbolInfoTick(_Symbol, last_tick) && last_tick.time > lastUpdate) {
      UpdateAllData();
      UpdateDisplay();
      lastUpdate = last_tick.time;
   }
}

//+------------------------------------------------------------------+
//| Normalização de Nomes de Pares                                   |
//+------------------------------------------------------------------+
void NormalizeAllPairNames() {
   for(int i = 0; i < MAX_PAIRS; i++) {
      string symbol = base_pairs[i];
      string suffix = StringSubstr(_Symbol, 6);
      normalized_pairs[i] = symbol + suffix;
      
      if(!SymbolSelect(normalized_pairs[i], true)) {
         normalized_pairs[i] = symbol;
         if(!SymbolSelect(normalized_pairs[i], true)) {
            normalized_pairs[i] = "";
         }
      }
   }
}

//+------------------------------------------------------------------+
//| LÓGICA DE CÁLCULO (Engine)                                      |
//+------------------------------------------------------------------+
void CalculatePairAnalysis(string symbol, PairAnalysisResult &result, ENUM_TIMEFRAMES timeframe) {
   result.trend = 0;
   result.score = 0;
   
   if(symbol == "") return;
   
   double ema5[], ema12[];
   ArraySetAsSeries(ema5, true);
   ArraySetAsSeries(ema12, true);
   
   int bars_to_check = 150;
   int handle_ema5 = iMA(symbol, timeframe, 5, 0, MODE_EMA, PRICE_CLOSE);
   int handle_ema12 = iMA(symbol, timeframe, 12, 0, MODE_EMA, PRICE_CLOSE);
   
   if(handle_ema5 == INVALID_HANDLE || handle_ema12 == INVALID_HANDLE) return;
   
   if(CopyBuffer(handle_ema5, 0, 0, bars_to_check, ema5) < 3 || 
      CopyBuffer(handle_ema12, 0, 0, bars_to_check, ema12) < 3) {
      IndicatorRelease(handle_ema5);
      IndicatorRelease(handle_ema12);
      return;
   }
   
   double price = SymbolInfoDouble(symbol, SYMBOL_ASK);
   if(price == 0) {
      IndicatorRelease(handle_ema5);
      IndicatorRelease(handle_ema12);
      return;
   }
   
   double distance = MathAbs(ema5[0] - ema12[0]) / price * 100;
   result.trend = (distance < 0.005) ? 0 : ((ema5[0] > ema12[0]) ? 1 : -1);
   
   if(result.trend != 0) {
      int score = 0;
      int min_size = MathMin(ArraySize(ema5), ArraySize(ema12));
      
      for(int i = 0; i < bars_to_check && i < min_size; i++) {
         if(((ema5[i] > ema12[i]) ? 1 : -1) == result.trend) 
            score++;
         else 
            break;
      }
      result.score = score;
   }
   
   IndicatorRelease(handle_ema5);
   IndicatorRelease(handle_ema12);
}

void UpdateAllData() {
   // Atualizar análise de todos os pares
   for(int i = 0; i < MAX_PAIRS; i++) {
      if(normalized_pairs[i] == "") continue;
      
      CalculatePairAnalysis(normalized_pairs[i], analysisCache[i].h4,  PERIOD_H4);
      CalculatePairAnalysis(normalized_pairs[i], analysisCache[i].d1,  PERIOD_D1);
      CalculatePairAnalysis(normalized_pairs[i], analysisCache[i].w1,  PERIOD_W1);
      CalculatePairAnalysis(normalized_pairs[i], analysisCache[i].m15, PERIOD_M15);
   }
   
   // Calcular força das moedas
   for(int i = 0; i < MAX_CURRENCIES; i++) {
      string currency = currencies[i];
      currencyData[i].name = currency;
      currencyData[i].h4_strength = 0;
      currencyData[i].h4_avg_score = 0;
      currencyData[i].d1_strength = 0;
      currencyData[i].d1_avg_score = 0;
      currencyData[i].w1_strength = 0;
      currencyData[i].w1_avg_score = 0;
      
      int h4_count = 0, d1_count = 0, w1_count = 0;
      
      for(int p = 0; p < MAX_PAIRS; p++) {
         if(normalized_pairs[p] == "") continue;
         
         string base = StringSubstr(base_pairs[p], 0, 3);
         string quote = StringSubstr(base_pairs[p], 3, 3);
         
         if(base != currency && quote != currency) continue;
         
         // H4
         int h4_dir = (base == currency) ? analysisCache[p].h4.trend : -analysisCache[p].h4.trend;
         currencyData[i].h4_strength += h4_dir;
         if(analysisCache[p].h4.trend != 0) {
            currencyData[i].h4_avg_score += analysisCache[p].h4.score;
            h4_count++;
         }
         
         // D1
         int d1_dir = (base == currency) ? analysisCache[p].d1.trend : -analysisCache[p].d1.trend;
         currencyData[i].d1_strength += d1_dir;
         if(analysisCache[p].d1.trend != 0) {
            currencyData[i].d1_avg_score += analysisCache[p].d1.score;
            d1_count++;
         }
         
         // W1
         int w1_dir = (base == currency) ? analysisCache[p].w1.trend : -analysisCache[p].w1.trend;
         currencyData[i].w1_strength += w1_dir;
         if(analysisCache[p].w1.trend != 0) {
            currencyData[i].w1_avg_score += analysisCache[p].w1.score;
            w1_count++;
         }
      }
      
      if(h4_count > 0) currencyData[i].h4_avg_score /= h4_count;
      if(d1_count > 0) currencyData[i].d1_avg_score /= d1_count;
      if(w1_count > 0) currencyData[i].w1_avg_score /= w1_count;
   }
   
   SortCurrenciesByStrength();
}

void SortCurrenciesByStrength() {
   for(int i = 0; i < MAX_CURRENCIES - 1; i++) {
      for(int j = i + 1; j < MAX_CURRENCIES; j++) {
         if(currencyData[i].d1_strength < currencyData[j].d1_strength) {
            CurrencyData temp = currencyData[i];
            currencyData[i] = currencyData[j];
            currencyData[j] = temp;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| FUNÇÕES DE UI - DESIGN PREMIUM PROFESSIONAL BLACK               |
//+------------------------------------------------------------------+
void UpdateDisplay() {
   ObjectsDeleteAll(0, obj_prefix);
   
   // ===== PAINEL PRINCIPAL COM EFEITO DE SOMBRA =====
   // Sombra externa (efeito de profundidade)
   CreateRectangle(obj_prefix + "shadow", PainelX+5, PainelY+5, PainelLargura, PainelAltura, C'8,10,12');
   
   // Borda externa azul elegante
   CreateRectangle(obj_prefix + "border_main", PainelX-3, PainelY-3, PainelLargura+6, PainelAltura+6, CorBordaDestaque);
   
   // Borda interna refinada
   CreateRectangle(obj_prefix + "border_inner", PainelX-1, PainelY-1, PainelLargura+2, PainelAltura+2, CorBordaPrincipal);
   
   // Fundo principal sofisticado
   CreateRectangle(obj_prefix + "bg", PainelX, PainelY, PainelLargura, PainelAltura, CorFundoPainel);
   
   DrawSignalSectionPremium();
   DrawStrategicMatrixPremium();
   DrawTacticalGridPremium();
   
   ChartRedraw();
}

void DrawSignalSectionPremium() {
   int x = PainelX + 35;
   int y = PainelY + 35;
   int section_width = PainelLargura - 70;
   int section_height = 90;
   
   // ===== SEÇÃO DE SINAL COM DESIGN PREMIUM =====
   // Sombra da seção
   CreateRectangle(obj_prefix + "signal_shadow", x+3, y+3, section_width, section_height, C'12,15,18');
   
   // Borda dupla elegante
   CreateRectangle(obj_prefix + "signal_border_out", x-2, y-2, section_width+4, section_height+4, CorBordaDestaque);
   CreateRectangle(obj_prefix + "signal_border_in", x-1, y-1, section_width+2, section_height+2, CorBordaPrincipal);
   
   // Fundo sofisticado com gradiente sutil
   CreateRectangle(obj_prefix + "signal_bg", x, y, section_width, section_height, CorFundoSinal);
   
   // Linha de destaque no topo
   CreateRectangle(obj_prefix + "signal_highlight", x, y, section_width, 3, CorBordaDestaque);
   
   // ===== TÍTULO PRINCIPAL COM ÍCONES =====
   int title_x = x + section_width/2;
   CreateLabel(obj_prefix + "signal_title", 
               "⚡ PAINEL DE SINAIS DE ALTA CONFLUÊNCIA V3.3 ⚡", 
               title_x, y + 15, CorTextoTitulo, 16, true, ANCHOR_CENTER, "Segoe UI");
   
   CreateLabel(obj_prefix + "signal_subtitle", 
               "🔥 PROFESSIONAL BLACK EDITION - PREMIUM DESIGN 🔥", 
               title_x, y + 35, CorBordaDestaque, 12, true, ANCHOR_CENTER, "Segoe UI");
   
   // ===== ANÁLISE DE SINAIS PREMIUM =====
   CurrencyData strongest = currencyData[0];
   CurrencyData weakest = currencyData[MAX_CURRENCIES - 1];
   int pair_index = FindPairIndex(strongest.name, weakest.name);
   
   if(pair_index == -1) return;
   
   string signal_pair = normalized_pairs[pair_index];
   PairAnalysisResult m15_trigger = analysisCache[pair_index].m15;
   
   // Seção dividida em 3 colunas elegantes
   int col_width = section_width / 3;
   
   // COLUNA 1: AÇÃO RECOMENDADA
   string action_text = "💰 COMPRAR " + signal_pair;
   CreateLabel(obj_prefix + "signal_action_title", "AÇÃO RECOMENDADA:", 
               x + 25, y + 58, CorTextoCorpo, 10, false, ANCHOR_LEFT, "Segoe UI");
   CreateLabel(obj_prefix + "signal_action", action_text, 
               x + 25, y + 75, CorTextoAcao, 12, true, ANCHOR_LEFT, "Segoe UI");
   
   // COLUNA 2: MOTIVO CENTRALIZADO
   string reason_text = "📊 " + strongest.name + " vs " + weakest.name + 
                       " | Força: " + StringFormat("%+d", strongest.d1_strength) + 
                       " vs " + StringFormat("%+d", weakest.d1_strength);
   CreateLabel(obj_prefix + "signal_reason_title", "ANÁLISE DE FORÇA:", 
               x + col_width, y + 58, CorTextoCorpo, 10, false, ANCHOR_LEFT, "Segoe UI");
   CreateLabel(obj_prefix + "signal_reason", reason_text, 
               x + col_width, y + 75, CorTextoMotivo, 11, false, ANCHOR_LEFT, "Segoe UI");
   
   // COLUNA 3: GATILHO M15
   QualityInfo q_info = GetQualityInfoPremium(m15_trigger.score, m15_trigger.trend);
   string trigger_text = GetTrendSymbolPremium(m15_trigger.trend) + " Score: " + 
                        IntegerToString(m15_trigger.score) + " (" + q_info.text + ")";
   CreateLabel(obj_prefix + "signal_trigger_title", "GATILHO M15:", 
               x + 2*col_width, y + 58, CorTextoCorpo, 10, false, ANCHOR_LEFT, "Segoe UI");
   CreateLabel(obj_prefix + "signal_trigger", trigger_text, 
               x + 2*col_width, y + 75, q_info.clr, 11, true, ANCHOR_LEFT, "Segoe UI");
}

void DrawStrategicMatrixPremium() {
   int x = PainelX + 35;
   int y = PainelY + 155;
   int section_width = PainelLargura - 70;
   
   // ===== TÍTULO DA SEÇÃO COM ÍCONE =====
   CreateLabel(obj_prefix + "matrix_title", 
               "📊 MATRIZ ESTRATÉGICA - VISÃO MACRO MULTI-TIMEFRAME", 
               x, y, CorTextoTitulo, 14, true, ANCHOR_LEFT, "Segoe UI");
   
   int start_y = y + 40;
   int row_height = 42;
   int col_widths[] = {110, 130, 130, 130, 130, 130, 130, 550};
   string headers[] = {"MOEDA", "H4 FORÇA", "H4 SCORE", "D1 FORÇA", "D1 SCORE", "W1 FORÇA", "W1 SCORE", "ANÁLISE DE CONSISTÊNCIA MULTI-TF"};
   
   int current_x = x;
   int table_width = 0;
   for(int i = 0; i < ArraySize(col_widths); i++) table_width += col_widths[i];
   
   // ===== CABEÇALHO PREMIUM =====
   // Sombra do cabeçalho
   CreateRectangle(obj_prefix + "matrix_header_shadow", x+3, start_y+3, table_width, row_height, C'12,15,18');
   
   // Bordas duplas elegantes
   CreateRectangle(obj_prefix + "matrix_header_border_out", x-2, start_y-2, table_width+4, row_height+4, CorBordaDestaque);
   CreateRectangle(obj_prefix + "matrix_header_border_in", x-1, start_y-1, table_width+2, row_height+2, CorBordaPrincipal);
   CreateRectangle(obj_prefix + "matrix_header_bg", x, start_y, table_width, row_height, CorFundoHeaderMatriz);
   
   // Linha de destaque azul no topo
   CreateRectangle(obj_prefix + "matrix_header_highlight", x, start_y, table_width, 3, CorBordaDestaque);
   
   // Headers com fonte premium
   current_x = x;
   for(int i = 0; i < ArraySize(headers); i++) {
      CreateLabel(obj_prefix + "h_" + IntegerToString(i), headers[i], 
                  current_x + col_widths[i]/2, start_y + row_height/2 - 10, 
                  CorTextoHeaderMatriz, TamanhoFonte+1, true, ANCHOR_CENTER, "Segoe UI");
      current_x += col_widths[i];
   }
   
   // ===== LINHAS DE DADOS PREMIUM =====
   for(int i = 0; i < MAX_CURRENCIES; i++) {
      int row_y = start_y + row_height + (i * row_height);
      current_x = x;
      
      // Sombra sutil para cada linha
      CreateRectangle(obj_prefix + "matrix_row_shadow_" + IntegerToString(i), 
                      x+2, row_y+2, table_width, row_height, C'10,12,15');
      
      // Fundo alternado sofisticado
      color row_bg = (i%2==0) ? CorFundoLinhaPar : CorFundoLinhaImpar;
      CreateRectangle(obj_prefix + "matrix_row_bg_" + IntegerToString(i), 
                      x, row_y, table_width, row_height, row_bg);
      
      // Destaque especial para as 3 primeiras posições
      if(i < 3) {
         CreateRectangle(obj_prefix + "matrix_row_highlight_" + IntegerToString(i), 
                         x, row_y, 5, row_height, CorBordaDestaque);
      }
      
      CurrencyData cd = currencyData[i];
      string i_str = IntegerToString(i);
      
      // MOEDA com emoji de posição
      string currency_display = GetRankEmoji(i) + " " + cd.name;
      CreateLabel(obj_prefix+"c_name_"+i_str, currency_display, 
                  current_x + col_widths[0]/2, row_y + row_height/2 - 10, 
                  CorTextoTitulo, TamanhoFonte+2, true, ANCHOR_CENTER, "Segoe UI");
      current_x += col_widths[0];
      
      // H4 FORÇA com cores sofisticadas
      CreateLabel(obj_prefix+"c_h4s_"+i_str, StringFormat("%+d",cd.h4_strength), 
                  current_x + col_widths[1]/2, row_y + row_height/2 - 10, 
                  GetScoreColorPremium(cd.h4_strength), TamanhoFonte+2, true, ANCHOR_CENTER, "Segoe UI");
      current_x += col_widths[1];
      
      // H4 SCORE
      CreateLabel(obj_prefix+"c_h4scr_"+i_str, DoubleToString(cd.h4_avg_score,1), 
                  current_x + col_widths[2]/2, row_y + row_height/2 - 10, 
                  CorTextoCorpo, TamanhoFonte, false, ANCHOR_CENTER, "Segoe UI");
      current_x += col_widths[2];
      
      // D1 FORÇA com destaque
      CreateLabel(obj_prefix+"c_d1s_"+i_str, StringFormat("%+d",cd.d1_strength), 
                  current_x + col_widths[3]/2, row_y + row_height/2 - 10, 
                  GetScoreColorPremium(cd.d1_strength), TamanhoFonte+2, true, ANCHOR_CENTER, "Segoe UI");
      current_x += col_widths[3];
      
      // D1 SCORE
      CreateLabel(obj_prefix+"c_d1scr_"+i_str, DoubleToString(cd.d1_avg_score,1), 
                  current_x + col_widths[4]/2, row_y + row_height/2 - 10, 
                  CorTextoCorpo, TamanhoFonte, false, ANCHOR_CENTER, "Segoe UI");
      current_x += col_widths[4];
      
      // W1 FORÇA
      CreateLabel(obj_prefix+"c_w1s_"+i_str, StringFormat("%+d",cd.w1_strength), 
                  current_x + col_widths[5]/2, row_y + row_height/2 - 10, 
                  GetScoreColorPremium(cd.w1_strength), TamanhoFonte+2, true, ANCHOR_CENTER, "Segoe UI");
      current_x += col_widths[5];
      
      // W1 SCORE
      CreateLabel(obj_prefix+"c_w1scr_"+i_str, DoubleToString(cd.w1_avg_score,1), 
                  current_x + col_widths[6]/2, row_y + row_height/2 - 10, 
                  CorTextoCorpo, TamanhoFonte, false, ANCHOR_CENTER, "Segoe UI");
      current_x += col_widths[6];
      
      // CONSISTÊNCIA com emojis
      string consistency_text = GetConsistencyStringPremium(cd);
      CreateLabel(obj_prefix+"c_cons_"+i_str, consistency_text, 
                  current_x + 25, row_y + row_height/2 - 10, 
                  CorTextoCorpo, TamanhoFonte, false, ANCHOR_LEFT, "Segoe UI");
   }
}

void DrawTacticalGridPremium() {
   int x = PainelX + 35;
   int y = PainelY + 580;
   
   // ===== TÍTULO DA SEÇÃO =====
   CreateLabel(obj_prefix + "grid_title", 
               "💰 GRELHA TÁTICA M15 - LAYOUT PANORÂMICO PREMIUM", 
               x, y, CorTextoTitulo, 14, true, ANCHOR_LEFT, "Segoe UI");
   
   int block_width = 450;
   int block_height = 185;
   int x_gap = 25;
   int y_gap = 25;
   int currency_index = 0;
   
   // ===== GRID 4x2 COM DESIGN PREMIUM =====
   for(int row = 0; row < 2; row++) {
      for(int col = 0; col < 4; col++) {
         if(currency_index >= MAX_CURRENCIES) break;
         
         int block_x = x + col * (block_width + x_gap);
         int block_y = y + 40 + row * (block_height + y_gap);
         
         string currency = currencies[currency_index];
         
         // ===== EFEITOS VISUAIS PREMIUM =====
         // Sombra do bloco
         CreateRectangle(obj_prefix+"block_shadow_"+currency, 
                         block_x+4, block_y+4, block_width, block_height, C'8,10,12');
         
         // Bordas duplas elegantes
         CreateRectangle(obj_prefix+"block_border_out_"+currency, 
                         block_x-2, block_y-2, block_width+4, block_height+4, CorBordaDestaque);
         CreateRectangle(obj_prefix+"block_border_in_"+currency, 
                         block_x-1, block_y-1, block_width+2, block_height+2, CorBordaBloco);
         
         // Fundo do bloco
         CreateRectangle(obj_prefix+"block_bg_"+currency, 
                         block_x, block_y, block_width, block_height, CorFundoBloco);
         
         // ===== CABEÇALHO DO BLOCO PREMIUM =====
         CreateRectangle(obj_prefix+"block_head_"+currency, 
                         block_x, block_y, block_width, 35, CorFundoHeaderBloco);
         CreateRectangle(obj_prefix+"block_head_highlight_"+currency, 
                         block_x, block_y, block_width, 3, CorBordaDestaque);
         
         // Título com emoji de moeda
         string currency_emoji = GetCurrencyEmoji(currency);
         string title = currency_emoji + " " + currency + " - ANÁLISE TÁTICA M15";
         CreateLabel(obj_prefix+"block_title_"+currency, title, 
                     block_x + block_width/2, block_y + 17, 
                     CorTituloBloco, TamanhoFonte+2, true, ANCHOR_CENTER, "Segoe UI");
         
         // ===== CONTEÚDO DO BLOCO =====
         DrawCurrencyBlockContent(currency, block_x, block_y + 40, block_width, block_height - 40);
         
         currency_index++;
      }
   }
}

//+------------------------------------------------------------------+
//| Funções Auxiliares de UI                                         |
//+------------------------------------------------------------------+
void CreateRectangle(string name, int x, int y, int width, int height, color clr) {
   ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_BGCOLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

void CreateLabel(string name, string text, int x, int y, color clr, int size, bool bold, ENUM_ANCHOR_POINT anchor, string font) {
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetString(0, name, OBJPROP_FONT, font);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, size);
   ObjectSetInteger(0, name, OBJPROP_ANCHOR, anchor);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   
   if(bold) {
      ObjectSetInteger(0, name, OBJPROP_FONTSIZE, size);
      ObjectSetString(0, name, OBJPROP_FONT, font + " Bold");
   }
}

void DrawCurrencyBlockContent(string currency, int x, int y, int width, int height) {
   int row_height = 25;
   int col1_x = x + 15;
   int col2_x = x + width/2 + 15;
   int current_y = y + 15;
   
   // Encontrar todos os pares relacionados
   for(int i = 0; i < MAX_PAIRS; i++) {
      if(normalized_pairs[i] == "") continue;
      
      string base = StringSubstr(base_pairs[i], 0, 3);
      string quote = StringSubstr(base_pairs[i], 3, 3);
      
      if(base == currency || quote == currency) {
         PairAnalysisResult m15 = analysisCache[i].m15;
         QualityInfo quality = GetQualityInfoPremium(m15.score, m15.trend);
         
         string pair_display = base_pairs[i];
         string trend_symbol = GetTrendSymbolPremium(m15.trend);
         string score_text = "Score: " + IntegerToString(m15.score);
         
         // Nome do par
         CreateLabel(obj_prefix + "grid_pair_" + currency + "_" + IntegerToString(i), 
                     pair_display, col1_x, current_y, CorTextoTitulo, TamanhoFonte, false, ANCHOR_LEFT, "Segoe UI");
         
         // Tendência e score
         CreateLabel(obj_prefix + "grid_trend_" + currency + "_" + IntegerToString(i), 
                     trend_symbol + " " + score_text, col1_x + 80, current_y, quality.clr, TamanhoFonte, true, ANCHOR_LEFT, "Segoe UI");
         
         // Qualidade
         CreateLabel(obj_prefix + "grid_quality_" + currency + "_" + IntegerToString(i), 
                     quality.emoji + " " + quality.text, col2_x, current_y, quality.clr, TamanhoFonte, false, ANCHOR_LEFT, "Segoe UI");
         
         current_y += row_height;
         
         if(current_y > y + height - row_height) break;
      }
   }
}

//+------------------------------------------------------------------+
//| Funções de Suporte                                               |
//+------------------------------------------------------------------+
int FindPairIndex(string curr1, string curr2) {
   string pair1 = curr1 + curr2;
   string pair2 = curr2 + curr1;
   
   for(int i = 0; i < MAX_PAIRS; i++) {
      if(base_pairs[i] == pair1 || base_pairs[i] == pair2) {
         return i;
      }
   }
   return -1;
}

color GetScoreColorPremium(int score) {
   if(score >= 3) return CorForcaAlta;
   if(score <= -3) return CorForcaBaixa;
   return CorForcaNeutra;
}

string GetTrendSymbolPremium(int trend) {
   if(trend > 0) return "↗️";
   if(trend < 0) return "↘️";
   return "➡️";
}

QualityInfo GetQualityInfoPremium(int score, int trend) {
   QualityInfo info;
   
   if(trend == 0) {
      info.text = "NULO";
      info.clr = CorQualidadeNula;
      info.emoji = "⚪";
   }
   else if(score >= 100) {
      info.text = "IDEAL";
      info.clr = CorQualidadeIdeal;
      info.emoji = "🟢";
   }
   else if(score >= 50) {
      info.text = "BOM";
      info.clr = CorQualidadeBom;
      info.emoji = "🟡";
   }
   else {
      info.text = "TARDIO";
      info.clr = CorQualidadeTardio;
      info.emoji = "🟠";
   }
   
   return info;
}

string GetRankEmoji(int rank) {
   switch(rank) {
      case 0: return "🥇";
      case 1: return "🥈";
      case 2: return "🥉";
      case 3: return "4️⃣";
      case 4: return "5️⃣";
      case 5: return "6️⃣";
      case 6: return "7️⃣";
      case 7: return "8️⃣";
   }
   return "";
}

string GetCurrencyEmoji(string currency) {
   if(currency == "USD") return "💵";
   if(currency == "EUR") return "💶";
   if(currency == "GBP") return "💷";
   if(currency == "JPY") return "💴";
   if(currency == "CHF") return "🇨🇭";
   if(currency == "CAD") return "🇨🇦";
   if(currency == "AUD") return "🇦🇺";
   if(currency == "NZD") return "🇳🇿";
   return "💱";
}

string GetConsistencyStringPremium(CurrencyData &cd) {
   string result = "";
   
   // Análise de consistência
   bool all_positive = (cd.h4_strength > 0 && cd.d1_strength > 0 && cd.w1_strength > 0);
   bool all_negative = (cd.h4_strength < 0 && cd.d1_strength < 0 && cd.w1_strength < 0);
   
   if(all_positive) {
      result = "✅ FORÇA CONSISTENTE EM TODOS OS TIMEFRAMES";
   }
   else if(all_negative) {
      result = "❌ FRAQUEZA CONSISTENTE EM TODOS OS TIMEFRAMES";
   }
   else {
      result = "⚠️ DIVERGÊNCIA ENTRE TIMEFRAMES - CAUTELA";
   }
   
   // Adicionar média de scores
   double avg_score = (cd.h4_avg_score + cd.d1_avg_score + cd.w1_avg_score) / 3.0;
   result += " | Média Score: " + DoubleToString(avg_score, 1);
   
   return result;
}

//+------------------------------------------------------------------+
//| Evento OnCalculate (necessário para indicadores)                 |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
   return(rates_total);
}